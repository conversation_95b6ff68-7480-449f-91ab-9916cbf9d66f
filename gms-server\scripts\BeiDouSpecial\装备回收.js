var 装备图标 = "#fEffect/CharacterEff/1062114/1/0#";  //爱心
var 正方箭头 = "#fUI/Basic/BtHide3/mouseOver/0#";
var Maper = Packages.server.MapleItemInformationProvider;
var 可回收金币数 = 24;
var 装备回收格子数 = 24;//X格子后会被回收
var itemList;
var 装备攻击对应的金币数 = new Array(
new Array(0,0),//Array(攻击/魔法力小于100,给0金币);这个不要去动后面这个0
new Array(30,5000),//Array(攻击/魔法力小于100,给0金币);这个不要去动后面这个0
new Array(50,40000),
new Array(60,90000),
new Array(70,100000),//Array(攻击/魔法力小于120,给150万金币);
new Array(80,300000),
new Array(90,350000),
new Array(100,400000),
new Array(130,500000),//所有区间段，是与上一个为大于，意思就是现在这个160给1000万，那就是大于150攻击小于160攻击给5000万。
new Array(9999,510000)//这里不要动前面，后面5000万为给的金币上限，你可以修改。
)
function start() {
    status = -1;
    action(1, 0, 0);
}

function action(mode, type, selection) {
    if (status == 0 && mode == 0) {
        cm.dispose();
        return;
    }
    if (mode == 1) {
        status++;
    } else {
        status--;
    }
      if(status == 0){
				text = "		"+装备图标+"		- #b#e装 备 分 解#k#n -       "+装备图标+"\r\n\r\n";
				text += "               #e#r- "+装备回收格子数+"格子以后的装备会被回收 -#n\r\n#b";
				text += "               #L888#【点击回收】#n\r\n\r\n#b";

				// 显示装备的部分已注释
				/*
				itemList = cm.getInventory(1).list().iterator();
				var indexof = 1;
				while (itemList.hasNext())
				{
					var item = itemList.next();
					var itemid = item.getItemId();
					if(item.getPosition()>装备回收格子数)
					{
						if (!Maper.getInstance().isCash(item.getItemId()))
						{
							if(Maper.getInstance().getReqLevel(item.getItemId())>=装备攻击对应的金币数[0][0])
							{
								text += "#v" + item.getItemId() + "#";
								if (indexof > 1 && indexof % 6 == 0) {
									text += "\r\n";
									} else if (cm.getMeso() >= 2000000000) {
										cm.sendOk("金币过多回收停止！");
										cm.dispose();
										return;
								}
							}
						}
					}
					indexof++;
				}
				*/
				cm.sendSimple(text);
			}

	  else if(status == 1)
	  {
		  if(selection == 888) {
			  // 点击回收时呼出9310022 NPC
			  cm.openNpc(9310022);
			  cm.dispose();
		  }
	  }
}